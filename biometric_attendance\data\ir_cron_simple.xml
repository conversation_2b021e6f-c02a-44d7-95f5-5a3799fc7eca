<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Simple Scheduled Action: Update Device Statistics -->
    <record id="ir_cron_update_device_stats" model="ir.cron">
        <field name="name">Biometric: Update Device Statistics</field>
        <field name="model_id" ref="model_biometric_device"/>
        <field name="state">code</field>
        <field name="code"><![CDATA[
# Update device statistics
devices = env['biometric.device'].search([])
for device in devices:
    device.total_employees = len(device.employee_ids)
    device.total_records = len(device.attendance_ids)
        ]]></field>
        <field name="interval_number">6</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="active">True</field>
        <field name="doall">False</field>
    </record>
</odoo>
