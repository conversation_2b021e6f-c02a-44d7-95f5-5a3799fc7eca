# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import socket
import serial
import logging

_logger = logging.getLogger(__name__)


class BiometricDevice(models.Model):
    _name = 'biometric.device'
    _description = 'Biometric Device Configuration'
    _order = 'name'

    name = fields.Char('Device Name', required=True)
    device_type = fields.Selection([
        ('zkteco', 'ZKTeco'),
        ('hikvision', 'Hikvision'),
        ('suprema', 'Suprema'),
        ('anviz', 'Anviz'),
        ('other', 'Other')
    ], string='Device Type', required=True, default='zkteco')
    
    connection_type = fields.Selection([
        ('tcp', 'TCP/IP'),
        ('serial', 'Serial Port'),
        ('usb', 'USB')
    ], string='Connection Type', required=True, default='tcp')
    
    # TCP/IP Settings
    ip_address = fields.Char('IP Address')
    port = fields.Integer('Port', default=4370)
    
    # Serial Settings
    serial_port = fields.Char('Serial Port', help='e.g., COM1, /dev/ttyUSB0')
    baud_rate = fields.Integer('Baud Rate', default=9600)
    
    # Device Settings
    device_id = fields.Integer('Device ID', default=1)
    password = fields.Char('Device Password')
    timeout = fields.Integer('Connection Timeout (seconds)', default=30)
    
    # Status
    active = fields.Boolean('Active', default=True)
    last_sync = fields.Datetime('Last Synchronization')
    status = fields.Selection([
        ('disconnected', 'Disconnected'),
        ('connected', 'Connected'),
        ('error', 'Error')
    ], string='Status', default='disconnected', readonly=True)
    
    # Statistics
    total_employees = fields.Integer('Total Employees', readonly=True)
    total_records = fields.Integer('Total Records', readonly=True)
    
    # Relations
    employee_ids = fields.One2many('biometric.employee', 'device_id', string='Employees')
    attendance_ids = fields.One2many('biometric.attendance', 'device_id', string='Attendance Records')
    
    @api.constrains('ip_address', 'connection_type')
    def _check_ip_address(self):
        for record in self:
            if record.connection_type == 'tcp' and not record.ip_address:
                raise ValidationError(_('IP Address is required for TCP/IP connection.'))
    
    @api.constrains('serial_port', 'connection_type')
    def _check_serial_port(self):
        for record in self:
            if record.connection_type == 'serial' and not record.serial_port:
                raise ValidationError(_('Serial Port is required for Serial connection.'))
    
    def test_connection(self):
        """Test connection to the biometric device"""
        self.ensure_one()
        try:
            if self.connection_type == 'tcp':
                return self._test_tcp_connection()
            elif self.connection_type == 'serial':
                return self._test_serial_connection()
            else:
                raise UserError(_('USB connection testing not implemented yet.'))
        except Exception as e:
            _logger.error("Connection test failed for device %s: %s" % (self.name, str(e)))
            self.status = 'error'
            raise UserError(_('Connection failed: %s') % str(e))
    
    def _test_tcp_connection(self):
        """Test TCP/IP connection"""
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(self.timeout)
        try:
            result = sock.connect_ex((self.ip_address, self.port))
            if result == 0:
                self.status = 'connected'
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Connection successful!'),
                        'type': 'success',
                    }
                }
            else:
                self.status = 'error'
                raise UserError(_('Cannot connect to device'))
        finally:
            sock.close()
    
    def _test_serial_connection(self):
        """Test Serial connection"""
        try:
            ser = serial.Serial(
                port=self.serial_port,
                baudrate=self.baud_rate,
                timeout=self.timeout
            )
            if ser.is_open:
                ser.close()
                self.status = 'connected'
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Serial connection successful!'),
                        'type': 'success',
                    }
                }
            else:
                self.status = 'error'
                raise UserError(_('Cannot open serial port'))
        except serial.SerialException as e:
            self.status = 'error'
            raise UserError(_('Serial connection error: %s') % str(e))
    
    def sync_employees(self):
        """Synchronize employees from device"""
        self.ensure_one()
        try:
            from ..lib.device_connector import DeviceDataSynchronizer
            synchronizer = DeviceDataSynchronizer(self, self.env)
            count = synchronizer.sync_employees()

            self.total_employees = len(self.employee_ids)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Sync Completed'),
                    'message': _('Synchronized %d employees from device: %s') % (count, self.name),
                    'type': 'success',
                }
            }
        except Exception as e:
            _logger.error("Employee sync failed: %s" % str(e))
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Sync Failed'),
                    'message': _('Employee synchronization failed: %s') % str(e),
                    'type': 'danger',
                }
            }

    def sync_attendance(self):
        """Synchronize attendance records from device"""
        self.ensure_one()
        try:
            from ..lib.device_connector import DeviceDataSynchronizer
            synchronizer = DeviceDataSynchronizer(self, self.env)
            count = synchronizer.sync_attendance()

            self.total_records = len(self.attendance_ids)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Sync Completed'),
                    'message': _('Synchronized %d attendance records from device: %s') % (count, self.name),
                    'type': 'success',
                }
            }
        except Exception as e:
            _logger.error("Attendance sync failed: %s" % str(e))
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Sync Failed'),
                    'message': _('Attendance synchronization failed: %s') % str(e),
                    'type': 'danger',
                }
            }

    def sync_all_data(self):
        """Synchronize all data from device"""
        self.ensure_one()
        try:
            emp_result = self.sync_employees()
            att_result = self.sync_attendance()

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Full Sync Completed'),
                    'message': _('Full synchronization completed for device: %s') % self.name,
                    'type': 'success',
                }
            }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Sync Failed'),
                    'message': _('Full synchronization failed: %s') % str(e),
                    'type': 'danger',
                }
            }

    def action_view_employees(self):
        """View employees for this device"""
        self.ensure_one()
        return {
            'name': _('Employees - %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'biometric.employee',
            'view_mode': 'tree,form',
            'domain': [('device_id', '=', self.id)],
            'context': {'default_device_id': self.id},
        }

    def action_view_attendance(self):
        """View attendance records for this device"""
        self.ensure_one()
        return {
            'name': _('Attendance Records - %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'biometric.attendance',
            'view_mode': 'tree,form',
            'domain': [('device_id', '=', self.id)],
            'context': {'default_device_id': self.id},
        }
